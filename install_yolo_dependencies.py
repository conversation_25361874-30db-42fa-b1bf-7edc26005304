#!/usr/bin/env python3
"""
Install YOLO dependencies for card detection
"""

import subprocess
import sys

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def main():
    """Install YOLO dependencies"""
    print("🚀 Installing YOLO dependencies for card detection...")
    print("=" * 50)
    
    # YOLO dependencies
    yolo_packages = [
        "ultralytics",
        "torch",
        "torchvision"
    ]
    
    success_count = 0
    
    for package in yolo_packages:
        print(f"\n📦 Installing {package}...")
        if install_package(package):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"✅ Successfully installed {success_count}/{len(yolo_packages)} packages")
    
    if success_count == len(yolo_packages):
        print("🎉 All YOLO dependencies installed successfully!")
        print("🎯 You can now run the card detection system with YOLO support")
    else:
        print("⚠️  Some packages failed to install. You may need to install them manually.")
    
    print("\n🚀 To start the system: python start_complete_system.py")

if __name__ == "__main__":
    main()
