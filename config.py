# Configuration file for Card Detection Application

# Gemini API Configuration
GEMINI_API_KEY = "AIzaSyD7C9w16htfeONGQ7C_gr1JuxfNbh9tyCw"

# Timing Configuration
COUNTDOWN_DELAY = 14.5  # seconds to wait after button press

# Card Analysis Configuration
CARD_SUITS = ['Hearts', 'Diamonds', 'Clubs', 'Spades']
CARD_VALUES = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']

# Hand Types
HAND_TYPES = {
    'HIGH_CARD': 'High Card',
    'PAIR': 'Pair',
    'SEQUENCE': 'Sequence',
    'COLOR': 'Color',
    'STRAIGHT_FLUSH': 'Straight Flush'
}

# Winning Seat Configuration
WINNING_SEATS = {
    'LEFT': 'A',
    'CENTER': 'B', 
    'RIGHT': 'C'
}

# GUI Configuration
WINDOW_TITLE = "Card Detection & Analysis"
WINDOW_SIZE = "800x600"
