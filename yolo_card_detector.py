"""
YOLO-based Card Detection Module
Uses YOLO for card detection and localization, then Gemini for card identification
"""

import cv2
import numpy as np
from PIL import Image, ImageGrab, ImageDraw
from typing import List, Dict, Tuple, Optional
import os
from datetime import datetime

class YOLOCardDetector:
    def __init__(self, model_path: Optional[str] = None):
        """Initialize YOLO card detector"""
        self.model = None
        self.model_path = model_path
        self.confidence_threshold = 0.5
        self.nms_threshold = 0.4
        
        # Initialize YOLO model
        self.load_model()
    
    def load_model(self):
        """Load YOLO model for card detection"""
        try:
            from ultralytics import <PERSON>OL<PERSON>
            
            if self.model_path and os.path.exists(self.model_path):
                print(f"Loading custom YOLO model from: {self.model_path}")
                self.model = YOLO(self.model_path)
            else:
                print("Loading pre-trained YOLO model (will be downloaded first time)")
                # Use YOLOv8 nano model as base - can be fine-tuned for cards
                self.model = YOLO('yolov8n.pt')
                
        except Exception as e:
            print(f"Error loading YOLO model: {e}")
            print("YOLO model not available - will use fallback detection")
            self.model = None
    
    def capture_screen_region(self, bbox: Tuple[int, int, int, int]) -> Image.Image:
        """Capture screen region using ImageGrab (like collect_training_ss.py)"""
        try:
            # Use ImageGrab.grab with bbox - same as your collect_training_ss.py
            image = ImageGrab.grab(bbox=bbox)
            print(f"Captured region: {bbox} -> Size: {image.size}")
            return image
        except Exception as e:
            print(f"Error capturing screen: {e}")
            raise
    
    def detect_cards_yolo(self, image: Image.Image) -> List[Dict]:
        """Detect cards using YOLO model"""
        if self.model is None:
            print("YOLO model not available, using fallback detection")
            return self.detect_cards_fallback(image)
        
        try:
            # Convert PIL to OpenCV format
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # Run YOLO detection
            results = self.model(cv_image, conf=self.confidence_threshold)
            
            detected_cards = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Get bounding box coordinates
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())
                        
                        detected_cards.append({
                            'bbox': (int(x1), int(y1), int(x2), int(y2)),
                            'confidence': float(confidence),
                            'class_id': class_id,
                            'center': (int((x1 + x2) / 2), int((y1 + y2) / 2))
                        })
            
            print(f"YOLO detected {len(detected_cards)} cards")
            return detected_cards
            
        except Exception as e:
            print(f"YOLO detection failed: {e}")
            return self.detect_cards_fallback(image)
    
    def detect_cards_fallback(self, image: Image.Image) -> List[Dict]:
        """Fallback card detection using basic computer vision"""
        try:
            # Convert to OpenCV
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            
            # Apply Gaussian blur
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # Edge detection
            edges = cv2.Canny(blurred, 50, 150)
            
            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            detected_cards = []
            
            for contour in contours:
                # Filter by area (cards should be reasonably sized)
                area = cv2.contourArea(contour)
                if area < 1000 or area > 50000:  # Adjust based on your card sizes
                    continue
                
                # Get bounding rectangle
                x, y, w, h = cv2.boundingRect(contour)
                
                # Filter by aspect ratio (cards are roughly rectangular)
                aspect_ratio = w / h
                if aspect_ratio < 0.5 or aspect_ratio > 2.0:
                    continue
                
                detected_cards.append({
                    'bbox': (x, y, x + w, y + h),
                    'confidence': 0.7,  # Default confidence for fallback
                    'class_id': 0,  # Generic card class
                    'center': (x + w // 2, y + h // 2)
                })
            
            print(f"Fallback detection found {len(detected_cards)} potential cards")
            return detected_cards
            
        except Exception as e:
            print(f"Fallback detection failed: {e}")
            return []
    
    def group_cards_by_position(self, cards: List[Dict], image_width: int) -> Dict[str, List[Dict]]:
        """Group detected cards by position (left, center, right)"""
        left_cards = []
        center_cards = []
        right_cards = []
        
        # Define regions based on image width
        left_boundary = image_width // 3
        right_boundary = 2 * image_width // 3
        
        for card in cards:
            center_x = card['center'][0]
            
            if center_x < left_boundary:
                left_cards.append(card)
            elif center_x > right_boundary:
                right_cards.append(card)
            else:
                center_cards.append(card)
        
        return {
            'left': left_cards,
            'center': center_cards,
            'right': right_cards
        }
    
    def extract_card_images(self, image: Image.Image, cards: List[Dict]) -> List[Image.Image]:
        """Extract individual card images from detected regions"""
        card_images = []
        
        for card in cards:
            x1, y1, x2, y2 = card['bbox']
            
            # Add some padding around the card
            padding = 10
            x1 = max(0, x1 - padding)
            y1 = max(0, y1 - padding)
            x2 = min(image.width, x2 + padding)
            y2 = min(image.height, y2 + padding)
            
            # Crop the card region
            card_image = image.crop((x1, y1, x2, y2))
            card_images.append(card_image)
        
        return card_images
    
    def visualize_detections(self, image: Image.Image, cards: List[Dict], 
                           grouped_cards: Dict[str, List[Dict]]) -> Image.Image:
        """Create visualization of detected cards with position labels"""
        # Create a copy for drawing
        vis_image = image.copy()
        draw = ImageDraw.Draw(vis_image)
        
        # Color mapping for positions
        colors = {
            'left': 'red',
            'center': 'green', 
            'right': 'blue'
        }
        
        # Draw bounding boxes and labels
        for position, position_cards in grouped_cards.items():
            color = colors.get(position, 'yellow')
            
            for i, card in enumerate(position_cards):
                x1, y1, x2, y2 = card['bbox']
                
                # Draw bounding box
                draw.rectangle([x1, y1, x2, y2], outline=color, width=3)
                
                # Draw label
                label = f"{position.upper()}_{i+1}"
                draw.text((x1, y1 - 20), label, fill=color)
        
        return vis_image
    
    def save_debug_images(self, original_image: Image.Image, 
                         detected_cards: List[Dict],
                         grouped_cards: Dict[str, List[Dict]],
                         card_images: List[Image.Image]) -> str:
        """Save debug images for analysis"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        debug_dir = "debug_images"
        os.makedirs(debug_dir, exist_ok=True)
        
        # Save original image
        original_path = os.path.join(debug_dir, f"original_{timestamp}.png")
        original_image.save(original_path)
        
        # Save visualization
        vis_image = self.visualize_detections(original_image, detected_cards, grouped_cards)
        vis_path = os.path.join(debug_dir, f"detections_{timestamp}.png")
        vis_image.save(vis_path)
        
        # Save individual card images
        for i, card_img in enumerate(card_images):
            card_path = os.path.join(debug_dir, f"card_{i+1}_{timestamp}.png")
            card_img.save(card_path)
        
        print(f"Debug images saved to: {debug_dir}")
        return debug_dir
    
    def analyze_screen_region(self, bbox: Tuple[int, int, int, int]) -> Dict:
        """Complete analysis pipeline: capture -> detect -> group"""
        try:
            # Step 1: Capture screen region (like collect_training_ss.py)
            image = self.capture_screen_region(bbox)
            
            # Step 2: Detect cards using YOLO
            detected_cards = self.detect_cards_yolo(image)
            
            # Step 3: Group cards by position
            grouped_cards = self.group_cards_by_position(detected_cards, image.width)
            
            # Step 4: Extract individual card images
            all_card_images = []
            for position_cards in grouped_cards.values():
                card_images = self.extract_card_images(image, position_cards)
                all_card_images.extend(card_images)
            
            # Step 5: Save debug images
            debug_dir = self.save_debug_images(image, detected_cards, grouped_cards, all_card_images)
            
            return {
                'original_image': image,
                'detected_cards': detected_cards,
                'grouped_cards': grouped_cards,
                'card_images': all_card_images,
                'debug_dir': debug_dir,
                'total_cards': len(detected_cards)
            }
            
        except Exception as e:
            print(f"Error in analysis pipeline: {e}")
            return {
                'error': str(e),
                'total_cards': 0
            }
