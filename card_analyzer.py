import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from PIL import Image, ImageTk
import mss
import google.generativeai as genai
from config import *
from utils import *
from database import CardAnalysisDB
from yolo_card_detector import YOLOCardDetector

class CardAnalyzer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title(WINDOW_TITLE)
        self.root.geometry(WINDOW_SIZE)

        # Initialize Gemini API
        genai.configure(api_key=GEMINI_API_KEY)
        self.model = genai.GenerativeModel('gemini-1.5-flash')

        # Initialize Database
        self.db = CardAnalysisDB()

        # Initialize YOLO Card Detector
        self.yolo_detector = YOLOCardDetector()

        # Region selection variables
        self.region_selected = False
        self.region_coords = None
        self.selection_window = None

        # Setup GUI
        self.setup_gui()
        
    def setup_gui(self):
        """Setup the main GUI interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Card Detection & Analysis", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Region selection section
        region_frame = ttk.LabelFrame(main_frame, text="Screen Region Selection", padding="10")
        region_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.select_region_btn = ttk.Button(region_frame, text="Select Screen Region", 
                                          command=self.select_region)
        self.select_region_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.region_status = ttk.Label(region_frame, text="No region selected")
        self.region_status.grid(row=0, column=1)
        
        # Analysis section
        analysis_frame = ttk.LabelFrame(main_frame, text="Analysis Control", padding="10")
        analysis_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(analysis_frame, text="Press when countdown reaches 1:").grid(row=0, column=0, padx=(0, 10))

        self.analyze_btn = ttk.Button(analysis_frame, text="ANALYZE NOW!",
                                    command=self.start_analysis, state='disabled')
        self.analyze_btn.grid(row=0, column=1)

        # Web Dashboard button
        self.web_btn = ttk.Button(analysis_frame, text="Open Web Dashboard",
                                command=self.open_web_dashboard)
        self.web_btn.grid(row=0, column=2, padx=(10, 0))
        
        # Status section
        self.status_label = ttk.Label(main_frame, text="Ready - Please select a region first")
        self.status_label.grid(row=3, column=0, columnspan=2, pady=(10, 0))
        
        # Results section
        results_frame = ttk.LabelFrame(main_frame, text="Analysis Results", padding="10")
        results_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        self.results_text = scrolledtext.ScrolledText(results_frame, height=20, width=80)
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
    
    def select_region(self):
        """Allow user to select screen region by dragging"""
        self.root.withdraw()  # Hide main window
        
        # Create fullscreen transparent window for selection
        self.selection_window = tk.Toplevel()
        self.selection_window.attributes('-fullscreen', True)
        self.selection_window.attributes('-alpha', 0.3)
        self.selection_window.configure(bg='black')
        
        # Variables for selection
        self.start_x = self.start_y = 0
        self.rect_id = None
        
        # Create canvas for drawing selection rectangle
        self.canvas = tk.Canvas(self.selection_window, highlightthickness=0)
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # Bind mouse events
        self.canvas.bind('<Button-1>', self.start_selection)
        self.canvas.bind('<B1-Motion>', self.update_selection)
        self.canvas.bind('<ButtonRelease-1>', self.end_selection)
        
        # Instructions
        instruction_text = "Click and drag to select the card game region. Release to confirm."
        self.canvas.create_text(self.selection_window.winfo_screenwidth()//2, 50, 
                               text=instruction_text, fill='white', font=('Arial', 16))
    
    def start_selection(self, event):
        """Start region selection"""
        self.start_x, self.start_y = event.x, event.y
        if self.rect_id:
            self.canvas.delete(self.rect_id)
    
    def update_selection(self, event):
        """Update selection rectangle"""
        if self.rect_id:
            self.canvas.delete(self.rect_id)
        self.rect_id = self.canvas.create_rectangle(
            self.start_x, self.start_y, event.x, event.y, 
            outline='red', width=2
        )
    
    def end_selection(self, event):
        """End region selection and store coordinates"""
        end_x, end_y = event.x, event.y
        
        # Store region coordinates (ensure proper order)
        self.region_coords = {
            'left': min(self.start_x, end_x),
            'top': min(self.start_y, end_y),
            'width': abs(end_x - self.start_x),
            'height': abs(end_y - self.start_y)
        }
        
        # Close selection window and show main window
        self.selection_window.destroy()
        self.root.deiconify()
        
        # Update status
        self.region_selected = True
        self.region_status.config(text=f"Region selected: {self.region_coords['width']}x{self.region_coords['height']}")
        self.analyze_btn.config(state='normal')
        self.status_label.config(text="Region selected! Ready to analyze.")
    
    def start_analysis(self):
        """Start the analysis process with countdown"""
        if not self.region_selected:
            messagebox.showerror("Error", "Please select a region first!")
            return
        
        # Disable button during analysis
        self.analyze_btn.config(state='disabled')
        self.status_label.config(text=f"Analysis will start in {COUNTDOWN_DELAY} seconds...")
        
        # Start countdown in separate thread
        threading.Thread(target=self.countdown_and_analyze, daemon=True).start()
    
    def countdown_and_analyze(self):
        """Countdown and then perform analysis"""
        # Wait for the specified delay
        time.sleep(COUNTDOWN_DELAY)
        
        # Update status
        self.root.after(0, lambda: self.status_label.config(text="Capturing screen..."))
        
        # Use YOLO-based detection and analysis
        try:
            print(f"Starting YOLO-based card detection for region: {self.region_coords}")

            # Convert region coords to bbox format (left, top, right, bottom)
            bbox = (
                self.region_coords['left'],
                self.region_coords['top'],
                self.region_coords['left'] + self.region_coords['width'],
                self.region_coords['top'] + self.region_coords['height']
            )

            # Run YOLO detection pipeline
            detection_result = self.yolo_detector.analyze_screen_region(bbox)

            if 'error' in detection_result:
                raise Exception(detection_result['error'])
            
            # Update status
            self.root.after(0, lambda: self.status_label.config(text="Analyzing cards with Gemini AI..."))

            # Analyze detected cards with Gemini
            analysis_result = self.analyze_detected_cards_with_gemini(detection_result)

            # Update results in main thread
            self.root.after(0, lambda: self.display_results(analysis_result))
            
        except Exception as e:
            error_msg = f"Error during analysis: {str(e)}"
            self.root.after(0, lambda: self.status_label.config(text=error_msg))
            self.root.after(0, lambda: messagebox.showerror("Analysis Error", error_msg))
        
        # Re-enable button
        self.root.after(0, lambda: self.analyze_btn.config(state='normal'))

    def analyze_detected_cards_with_gemini(self, detection_result: dict) -> dict:
        """Analyze YOLO-detected cards using Gemini AI"""
        try:
            print(f"YOLO detected {detection_result.get('total_cards', 0)} cards")
            print(f"Debug images saved to: {detection_result.get('debug_dir', 'N/A')}")

            # Get the original image and grouped cards
            original_image = detection_result.get('original_image')
            grouped_cards = detection_result.get('grouped_cards', {})

            if not original_image:
                raise Exception("No image captured from YOLO detection")

            # Create enhanced prompt with YOLO detection info
            prompt = self.create_yolo_enhanced_prompt(grouped_cards)

            # Analyze with Gemini using the original image
            response = self.analyze_image_with_gemini(original_image, prompt)

            # Parse response and enhance with YOLO data
            analysis = parse_card_analysis(response.text)

            # Add YOLO detection information
            analysis['yolo_detections'] = {
                'total_cards': detection_result.get('total_cards', 0),
                'left_cards_detected': len(grouped_cards.get('left', [])),
                'center_cards_detected': len(grouped_cards.get('center', [])),
                'right_cards_detected': len(grouped_cards.get('right', [])),
                'debug_dir': detection_result.get('debug_dir', '')
            }

            # Add brightness analysis
            brightest_region = analyze_brightness_regions(original_image)
            analysis['brightness_analysis'] = brightest_region
            analysis['winning_seat'] = WINNING_SEATS.get(brightest_region, 'Unknown')

            # Store raw response and region coordinates for database
            analysis['raw_response'] = response.text
            analysis['region_coords'] = self.region_coords

            # Save to database
            try:
                session_id = self.db.save_analysis_result(analysis)
                analysis['session_id'] = session_id
                print(f"Analysis saved to database with ID: {session_id}")
            except Exception as db_error:
                print(f"Failed to save to database: {db_error}")

            return analysis

        except Exception as e:
            print(f"Gemini analysis error: {e}")
            return {
                'error': str(e),
                'left_cards': {'cards': [], 'hand_type': 'Error'},
                'center_cards': {'cards': [], 'hand_type': 'Error'},
                'right_cards': {'cards': [], 'hand_type': 'Error'},
                'winning_seat': 'Unknown',
                'yolo_detections': detection_result.get('yolo_detections', {})
            }

    def create_yolo_enhanced_prompt(self, grouped_cards: dict) -> str:
        """Create Gemini prompt enhanced with YOLO detection information"""
        left_count = len(grouped_cards.get('left', []))
        center_count = len(grouped_cards.get('center', []))
        right_count = len(grouped_cards.get('right', []))

        return f"""
        I have used YOLO object detection to identify card locations in this image:
        - LEFT section: {left_count} cards detected
        - CENTER section: {center_count} cards detected
        - RIGHT section: {right_count} cards detected

        Now I need you to identify the specific cards in each section. Look at the image and:

        LEFT SIDE (Seat A) - {left_count} cards detected:
        - Identify each card's value (A, 2-10, J, Q, K) and suit (Hearts, Diamonds, Clubs, Spades)
        - Determine hand type: High Card, Pair, Sequence, Color, or Straight Flush

        CENTER (Seat B) - {center_count} cards detected:
        - Identify each card's value and suit
        - Determine hand type: High Card, Pair, Sequence, Color, or Straight Flush

        RIGHT SIDE (Seat C) - {right_count} cards detected:
        - Identify each card's value and suit
        - Determine hand type: High Card, Pair, Sequence, Color, or Straight Flush

        BRIGHTNESS ANALYSIS:
        - Determine which section (left, center, right) appears brightest or most highlighted

        Format your response EXACTLY like this:

        LEFT SIDE (Seat A):
        Cards: [card1], [card2], [card3]
        Hand Type: [type]

        CENTER (Seat B):
        Cards: [card1], [card2], [card3]
        Hand Type: [type]

        RIGHT SIDE (Seat C):
        Cards: [card1], [card2], [card3]
        Hand Type: [type]

        WINNING SEAT: [A/B/C]

        Use standard notation: A, 2, 3, 4, 5, 6, 7, 8, 9, 10, J, Q, K for values and Hearts, Diamonds, Clubs, Spades for suits.
        """

    def analyze_image_with_gemini(self, image: Image.Image, prompt: str):

    def analyze_image_with_gemini(self, image: Image.Image, prompt: str):
        """Analyze image using Gemini API with custom prompt"""
        try:
            # Convert image to RGB if needed
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Try direct image approach first (works with most Gemini versions)
            response = self.model.generate_content([prompt, image])
            return response

        except Exception as e:
            print(f"Gemini API error: {e}")
            raise
    
    def display_results(self, analysis: dict):
        """Display analysis results"""
        self.results_text.delete(1.0, tk.END)
        
        if 'error' in analysis:
            self.results_text.insert(tk.END, f"ERROR: {analysis['error']}\n\n")
        
        # Format and display results
        results = "=== CARD ANALYSIS RESULTS ===\n\n"
        
        # Left cards (Seat A)
        results += "LEFT SIDE (Seat A):\n"
        results += f"Cards: {', '.join(analysis['left_cards']['cards']) if analysis['left_cards']['cards'] else 'Not detected'}\n"
        results += f"Hand Type: {analysis['left_cards']['hand_type']}\n\n"
        
        # Center cards (Seat B)
        results += "CENTER (Seat B):\n"
        results += f"Cards: {', '.join(analysis['center_cards']['cards']) if analysis['center_cards']['cards'] else 'Not detected'}\n"
        results += f"Hand Type: {analysis['center_cards']['hand_type']}\n\n"
        
        # Right cards (Seat C)
        results += "RIGHT SIDE (Seat C):\n"
        results += f"Cards: {', '.join(analysis['right_cards']['cards']) if analysis['right_cards']['cards'] else 'Not detected'}\n"
        results += f"Hand Type: {analysis['right_cards']['hand_type']}\n\n"
        
        # Winning seat
        results += f"🏆 WINNING SEAT: {analysis['winning_seat']}\n"
        if 'brightness_analysis' in analysis:
            results += f"(Based on brightest region: {analysis['brightness_analysis']})\n"

        # Add YOLO detection info if available
        if 'yolo_detections' in analysis:
            yolo_info = analysis['yolo_detections']
            results += f"\n🎯 YOLO DETECTION INFO:\n"
            results += f"Total Cards Detected: {yolo_info.get('total_cards', 0)}\n"
            results += f"Left: {yolo_info.get('left_cards_detected', 0)} cards\n"
            results += f"Center: {yolo_info.get('center_cards_detected', 0)} cards\n"
            results += f"Right: {yolo_info.get('right_cards_detected', 0)} cards\n"
            if yolo_info.get('debug_dir'):
                results += f"Debug Images: {yolo_info['debug_dir']}\n"

        self.results_text.insert(tk.END, results)

        # Add database info if available
        if 'session_id' in analysis and analysis['session_id'] > 0:
            self.results_text.insert(tk.END, f"\n📊 Data saved to database (ID: {analysis['session_id']})")
            self.results_text.insert(tk.END, "\n🌐 View in web dashboard: http://localhost:5000")

        self.status_label.config(text="Analysis complete!")

    def open_web_dashboard(self):
        """Open web dashboard in browser"""
        import webbrowser
        try:
            webbrowser.open('http://localhost:5000')
        except Exception as e:
            messagebox.showinfo("Web Dashboard",
                              "Please start the web server first:\npython web_app.py\n\nThen visit: http://localhost:5000")

    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = CardAnalyzer()
    app.run()
