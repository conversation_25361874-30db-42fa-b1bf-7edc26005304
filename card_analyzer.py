import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from PIL import Image, ImageTk
import mss
import google.generativeai as genai
from config import *
from utils import *
from database import CardAnalysisDB

class CardAnalyzer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title(WINDOW_TITLE)
        self.root.geometry(WINDOW_SIZE)

        # Initialize Gemini API
        genai.configure(api_key=GEMINI_API_KEY)
        self.model = genai.GenerativeModel('gemini-1.5-flash')

        # Initialize Database
        self.db = CardAnalysisDB()

        # Region selection variables
        self.region_selected = False
        self.region_coords = None
        self.selection_window = None

        # Setup GUI
        self.setup_gui()
        
    def setup_gui(self):
        """Setup the main GUI interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Card Detection & Analysis", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Region selection section
        region_frame = ttk.LabelFrame(main_frame, text="Screen Region Selection", padding="10")
        region_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.select_region_btn = ttk.Button(region_frame, text="Select Screen Region", 
                                          command=self.select_region)
        self.select_region_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.region_status = ttk.Label(region_frame, text="No region selected")
        self.region_status.grid(row=0, column=1)
        
        # Analysis section
        analysis_frame = ttk.LabelFrame(main_frame, text="Analysis Control", padding="10")
        analysis_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(analysis_frame, text="Press when countdown reaches 1:").grid(row=0, column=0, padx=(0, 10))

        self.analyze_btn = ttk.Button(analysis_frame, text="ANALYZE NOW!",
                                    command=self.start_analysis, state='disabled')
        self.analyze_btn.grid(row=0, column=1)

        # Web Dashboard button
        self.web_btn = ttk.Button(analysis_frame, text="Open Web Dashboard",
                                command=self.open_web_dashboard)
        self.web_btn.grid(row=0, column=2, padx=(10, 0))
        
        # Status section
        self.status_label = ttk.Label(main_frame, text="Ready - Please select a region first")
        self.status_label.grid(row=3, column=0, columnspan=2, pady=(10, 0))
        
        # Results section
        results_frame = ttk.LabelFrame(main_frame, text="Analysis Results", padding="10")
        results_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        self.results_text = scrolledtext.ScrolledText(results_frame, height=20, width=80)
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
    
    def select_region(self):
        """Allow user to select screen region by dragging"""
        self.root.withdraw()  # Hide main window
        
        # Create fullscreen transparent window for selection
        self.selection_window = tk.Toplevel()
        self.selection_window.attributes('-fullscreen', True)
        self.selection_window.attributes('-alpha', 0.3)
        self.selection_window.configure(bg='black')
        
        # Variables for selection
        self.start_x = self.start_y = 0
        self.rect_id = None
        
        # Create canvas for drawing selection rectangle
        self.canvas = tk.Canvas(self.selection_window, highlightthickness=0)
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # Bind mouse events
        self.canvas.bind('<Button-1>', self.start_selection)
        self.canvas.bind('<B1-Motion>', self.update_selection)
        self.canvas.bind('<ButtonRelease-1>', self.end_selection)
        
        # Instructions
        instruction_text = "Click and drag to select the card game region. Release to confirm."
        self.canvas.create_text(self.selection_window.winfo_screenwidth()//2, 50, 
                               text=instruction_text, fill='white', font=('Arial', 16))
    
    def start_selection(self, event):
        """Start region selection"""
        self.start_x, self.start_y = event.x, event.y
        if self.rect_id:
            self.canvas.delete(self.rect_id)
    
    def update_selection(self, event):
        """Update selection rectangle"""
        if self.rect_id:
            self.canvas.delete(self.rect_id)
        self.rect_id = self.canvas.create_rectangle(
            self.start_x, self.start_y, event.x, event.y, 
            outline='red', width=2
        )
    
    def end_selection(self, event):
        """End region selection and store coordinates"""
        end_x, end_y = event.x, event.y
        
        # Store region coordinates (ensure proper order)
        self.region_coords = {
            'left': min(self.start_x, end_x),
            'top': min(self.start_y, end_y),
            'width': abs(end_x - self.start_x),
            'height': abs(end_y - self.start_y)
        }
        
        # Close selection window and show main window
        self.selection_window.destroy()
        self.root.deiconify()
        
        # Update status
        self.region_selected = True
        self.region_status.config(text=f"Region selected: {self.region_coords['width']}x{self.region_coords['height']}")
        self.analyze_btn.config(state='normal')
        self.status_label.config(text="Region selected! Ready to analyze.")
    
    def start_analysis(self):
        """Start the analysis process with countdown"""
        if not self.region_selected:
            messagebox.showerror("Error", "Please select a region first!")
            return
        
        # Disable button during analysis
        self.analyze_btn.config(state='disabled')
        self.status_label.config(text=f"Analysis will start in {COUNTDOWN_DELAY} seconds...")
        
        # Start countdown in separate thread
        threading.Thread(target=self.countdown_and_analyze, daemon=True).start()
    
    def countdown_and_analyze(self):
        """Countdown and then perform analysis"""
        # Wait for the specified delay
        time.sleep(COUNTDOWN_DELAY)
        
        # Update status
        self.root.after(0, lambda: self.status_label.config(text="Capturing screen..."))
        
        # Capture the selected region using multiple methods
        try:
            print(f"Capturing region: {self.region_coords}")
            image = self.capture_top_window_region()
            print(f"Captured image size: {image.size}, mode: {image.mode}")
            
            # Update status
            self.root.after(0, lambda: self.status_label.config(text="Analyzing with Gemini AI..."))
            
            # Analyze with Gemini
            analysis_result = self.analyze_with_gemini(image)
            
            # Update results in main thread
            self.root.after(0, lambda: self.display_results(analysis_result))
            
        except Exception as e:
            error_msg = f"Error during analysis: {str(e)}"
            self.root.after(0, lambda: self.status_label.config(text=error_msg))
            self.root.after(0, lambda: messagebox.showerror("Analysis Error", error_msg))
        
        # Re-enable button
        self.root.after(0, lambda: self.analyze_btn.config(state='normal'))

    def capture_top_window_region(self) -> Image.Image:
        """Capture the selected region from the topmost window using multiple methods"""
        import time

        # Method 1: Try PyAutoGUI (captures what's actually visible on screen)
        try:
            import pyautogui
            print("Trying PyAutoGUI capture method...")

            # Small delay to ensure game window is focused
            time.sleep(0.5)

            # Capture the region using PyAutoGUI
            screenshot = pyautogui.screenshot(region=(
                self.region_coords['left'],
                self.region_coords['top'],
                self.region_coords['width'],
                self.region_coords['height']
            ))

            print("PyAutoGUI capture successful!")
            return screenshot

        except Exception as e:
            print(f"PyAutoGUI failed: {e}")

        # Method 2: Try alternative PyAutoGUI with focus handling
        try:
            print("Trying PyAutoGUI with window focus...")

            # Try to bring our window to front briefly, then capture
            self.root.lift()
            self.root.attributes('-topmost', True)
            time.sleep(0.1)
            self.root.attributes('-topmost', False)

            # Now capture
            screenshot = pyautogui.screenshot()

            # Crop to our region
            cropped = screenshot.crop((
                self.region_coords['left'],
                self.region_coords['top'],
                self.region_coords['left'] + self.region_coords['width'],
                self.region_coords['top'] + self.region_coords['height']
            ))

            print("Alternative PyAutoGUI capture successful!")
            return cropped

        except Exception as e:
            print(f"Alternative PyAutoGUI failed: {e}")

        # Method 3: Try Windows API approach (if available)
        try:
            import win32gui
            import win32ui
            import win32con

            print("Trying Windows API capture method...")

            # Get desktop window
            hwnd = win32gui.GetDesktopWindow()

            # Get window device context
            wDC = win32gui.GetWindowDC(hwnd)
            dcObj = win32ui.CreateDCFromHandle(wDC)
            cDC = dcObj.CreateCompatibleDC()

            # Create bitmap
            dataBitMap = win32ui.CreateBitmap()
            dataBitMap.CreateCompatibleBitmap(dcObj, self.region_coords['width'], self.region_coords['height'])
            cDC.SelectObject(dataBitMap)

            # Copy screen content
            cDC.BitBlt((0, 0), (self.region_coords['width'], self.region_coords['height']),
                      dcObj, (self.region_coords['left'], self.region_coords['top']), win32con.SRCCOPY)

            # Convert to PIL Image
            bmpinfo = dataBitMap.GetInfo()
            bmpstr = dataBitMap.GetBitmapBits(True)

            image = Image.frombuffer(
                'RGB',
                (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
                bmpstr, 'raw', 'BGRX', 0, 1
            )

            # Clean up
            dcObj.DeleteDC()
            cDC.DeleteDC()
            win32gui.ReleaseDC(hwnd, wDC)
            win32gui.DeleteObject(dataBitMap.GetHandle())

            print("Windows API capture successful!")
            return image

        except Exception as e:
            print(f"Windows API failed: {e}")

        # Method 4: Fallback to MSS with delay and window management
        try:
            print("Trying MSS with window management...")

            # Try to minimize our window temporarily
            self.root.withdraw()
            time.sleep(0.5)

            with mss.mss() as sct:
                screenshot = sct.grab(self.region_coords)
                image = Image.frombytes('RGB', screenshot.size, screenshot.bgra, 'raw', 'BGRX')

            # Restore our window
            self.root.deiconify()

            print("MSS capture completed!")
            return image

        except Exception as e:
            print(f"MSS also failed: {e}")

            # Final fallback - restore window and raise error
            try:
                self.root.deiconify()
            except:
                pass
            raise Exception("All capture methods failed!")

    def analyze_with_gemini(self, image: Image.Image) -> dict:
        """Analyze image using Gemini API"""
        try:
            # Create prompt
            prompt = create_analysis_prompt()

            # Convert image to RGB if needed and save temporarily
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Save image for debugging
            debug_path = f"captured_region_{int(time.time())}.png"
            image.save(debug_path)
            print(f"Debug: Saved captured image to {debug_path}")

            # Save image temporarily for Gemini
            import tempfile
            import os
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                image.save(tmp_file.name, 'PNG')
                temp_image_path = tmp_file.name

            try:
                # Try direct image approach first (works with most Gemini versions)
                response = self.model.generate_content([prompt, image])

                # Clean up temporary file
                os.unlink(temp_image_path)

            except Exception as direct_error:
                print(f"Direct image method failed: {direct_error}")

                try:
                    # Fallback: try with file upload if available
                    import google.generativeai as genai
                    if hasattr(genai, 'upload_file'):
                        uploaded_file = genai.upload_file(temp_image_path)
                        response = self.model.generate_content([prompt, uploaded_file])
                    else:
                        # Use PIL Image directly
                        response = self.model.generate_content([prompt, image])

                    # Clean up temporary file
                    os.unlink(temp_image_path)

                except Exception as fallback_error:
                    print(f"All image methods failed: {fallback_error}")
                    os.unlink(temp_image_path)
                    raise fallback_error

            # Parse response
            analysis = parse_card_analysis(response.text)

            # Add brightness analysis
            brightest_region = analyze_brightness_regions(image)
            analysis['brightness_analysis'] = brightest_region
            analysis['winning_seat'] = WINNING_SEATS.get(brightest_region, 'Unknown')

            # Store raw response and region coordinates for database
            analysis['raw_response'] = response.text
            analysis['region_coords'] = self.region_coords

            # Save to database
            try:
                session_id = self.db.save_analysis_result(analysis)
                analysis['session_id'] = session_id
                print(f"Analysis saved to database with ID: {session_id}")
            except Exception as db_error:
                print(f"Failed to save to database: {db_error}")

            return analysis

        except Exception as e:
            print(f"Gemini analysis error: {e}")
            return {
                'error': str(e),
                'left_cards': {'cards': [], 'hand_type': 'Error'},
                'center_cards': {'cards': [], 'hand_type': 'Error'},
                'right_cards': {'cards': [], 'hand_type': 'Error'},
                'winning_seat': 'Unknown'
            }
    
    def display_results(self, analysis: dict):
        """Display analysis results"""
        self.results_text.delete(1.0, tk.END)
        
        if 'error' in analysis:
            self.results_text.insert(tk.END, f"ERROR: {analysis['error']}\n\n")
        
        # Format and display results
        results = "=== CARD ANALYSIS RESULTS ===\n\n"
        
        # Left cards (Seat A)
        results += "LEFT SIDE (Seat A):\n"
        results += f"Cards: {', '.join(analysis['left_cards']['cards']) if analysis['left_cards']['cards'] else 'Not detected'}\n"
        results += f"Hand Type: {analysis['left_cards']['hand_type']}\n\n"
        
        # Center cards (Seat B)
        results += "CENTER (Seat B):\n"
        results += f"Cards: {', '.join(analysis['center_cards']['cards']) if analysis['center_cards']['cards'] else 'Not detected'}\n"
        results += f"Hand Type: {analysis['center_cards']['hand_type']}\n\n"
        
        # Right cards (Seat C)
        results += "RIGHT SIDE (Seat C):\n"
        results += f"Cards: {', '.join(analysis['right_cards']['cards']) if analysis['right_cards']['cards'] else 'Not detected'}\n"
        results += f"Hand Type: {analysis['right_cards']['hand_type']}\n\n"
        
        # Winning seat
        results += f"🏆 WINNING SEAT: {analysis['winning_seat']}\n"
        if 'brightness_analysis' in analysis:
            results += f"(Based on brightest region: {analysis['brightness_analysis']})\n"
        
        self.results_text.insert(tk.END, results)

        # Add database info if available
        if 'session_id' in analysis and analysis['session_id'] > 0:
            self.results_text.insert(tk.END, f"\n📊 Data saved to database (ID: {analysis['session_id']})")
            self.results_text.insert(tk.END, "\n🌐 View in web dashboard: http://localhost:5000")

        self.status_label.config(text="Analysis complete!")

    def open_web_dashboard(self):
        """Open web dashboard in browser"""
        import webbrowser
        try:
            webbrowser.open('http://localhost:5000')
        except Exception as e:
            messagebox.showinfo("Web Dashboard",
                              "Please start the web server first:\npython web_app.py\n\nThen visit: http://localhost:5000")

    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = CardAnalyzer()
    app.run()
