import os
import time
from datetime import datetime
from PIL import ImageGrab

# === CONFIGURATION ===
CROP_BOX = (970, 388, 1350, 632)
TOTAL_SCREENSHOTS = 100
DELAY_FIRST = 14.5
DELAY_BETWEEN = 56
SAVE_DIR = "images/train"

# === SETUP ===
os.makedirs(SAVE_DIR, exist_ok=True)
print(f"📁 Saving screenshots to: {SAVE_DIR}")

input("⏳ Press ENTER to begin screenshot collection when game starts...")
print(f"⏱ Waiting {DELAY_FIRST} seconds for first screenshot...")
time.sleep(DELAY_FIRST)

for i in range(1, TOTAL_SCREENSHOTS + 1):
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"ss_{i:03d}_{timestamp}.jpg"
    path = os.path.join(SAVE_DIR, filename)

    img = ImageGrab.grab(bbox=CROP_BOX)
    img.save(path)
    print(f"✅ Saved screenshot {i}/{TOTAL_SCREENSHOTS}: {filename}")

    if i < TOTAL_SCREENSHOTS:
        print(f"⏳ Waiting {DELAY_BETWEEN} seconds for next screenshot...\n")
        time.sleep(DELAY_BETWEEN)

print("\n🎉 Screenshot collection complete. Ready for labeling.")
