{% extends "base.html" %}

{% block title %}All Analyses - Card Analysis{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4"><i class="fas fa-table"></i> All Analyses</h1>
    </div>
</div>

<!-- Filters and Controls -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="limitSelect" class="form-label">Show Records:</label>
                        <select class="form-select" id="limitSelect" onchange="loadAnalyses()">
                            <option value="25">25</option>
                            <option value="50" selected>50</option>
                            <option value="100">100</option>
                            <option value="200">200</option>
                            <option value="500">500</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="seatFilter" class="form-label">Filter by Winning Seat:</label>
                        <select class="form-select" id="seatFilter" onchange="filterTable()">
                            <option value="">All Seats</option>
                            <option value="A">Seat A (Left)</option>
                            <option value="B">Seat B (Center)</option>
                            <option value="C">Seat C (Right)</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="searchInput" class="form-label">Search:</label>
                        <input type="text" class="form-control" id="searchInput" placeholder="Search analyses..." onkeyup="filterTable()">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-success d-block w-100" onclick="exportData()">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body">
                <h5><i class="fas fa-info-circle"></i> Quick Stats</h5>
                <p class="mb-1">Total Records: <span id="totalRecords">-</span></p>
                <p class="mb-1">Visible Records: <span id="visibleRecords">-</span></p>
                <p class="mb-0">Last Updated: <span id="lastUpdated">-</span></p>
            </div>
        </div>
    </div>
</div>

<!-- Main Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list"></i> Analysis Records</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="analysesTable">
                        <thead class="table-dark">
                            <tr>
                                <th onclick="sortTable(0)">
                                    ID <i class="fas fa-sort"></i>
                                </th>
                                <th onclick="sortTable(1)">
                                    Timestamp <i class="fas fa-sort"></i>
                                </th>
                                <th onclick="sortTable(2)">
                                    Winning Seat <i class="fas fa-sort"></i>
                                </th>
                                <th onclick="sortTable(3)">
                                    Brightness <i class="fas fa-sort"></i>
                                </th>
                                <th>Left (A)</th>
                                <th>Center (B)</th>
                                <th>Right (C)</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="analyses-table-body">
                            <tr>
                                <td colspan="8" class="text-center">
                                    <div class="loading">
                                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                                        <p>Loading analyses...</p>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Analysis Details Modal -->
<div class="modal fade" id="analysisModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Detailed Analysis</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="analysis-details">
                <!-- Details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="printAnalysis()">
                    <i class="fas fa-print"></i> Print
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let allAnalyses = [];
let currentSort = { column: 0, direction: 'desc' };

// Load all analyses
async function loadAnalyses() {
    const limit = document.getElementById('limitSelect').value;
    showLoading('analyses-table-body');
    
    try {
        const response = await fetch(`/api/recent-analyses?limit=${limit}`);
        allAnalyses = await response.json();
        
        // Load detailed data for each analysis
        for (let analysis of allAnalyses) {
            const detailResponse = await fetch(`/api/analysis/${analysis.id}`);
            const details = await detailResponse.json();
            analysis.details = details;
        }
        
        displayAnalyses(allAnalyses);
        updateStats();
        
    } catch (error) {
        console.error('Error loading analyses:', error);
        showError('analyses-table-body', 'Failed to load analyses');
    }
}

// Display analyses in table
function displayAnalyses(analyses) {
    const tableBody = document.getElementById('analyses-table-body');
    
    if (analyses.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="8" class="text-center">No analyses found</td></tr>';
        return;
    }
    
    tableBody.innerHTML = analyses.map(analysis => {
        const details = analysis.details || {};
        const positions = details.positions || [];
        
        const leftPos = positions.find(p => p.position === 'LEFT') || {};
        const centerPos = positions.find(p => p.position === 'CENTER') || {};
        const rightPos = positions.find(p => p.position === 'RIGHT') || {};
        
        return `
            <tr class="${getWinningSeatClass(analysis.winning_seat)}" data-analysis='${JSON.stringify(analysis)}'>
                <td>${analysis.id}</td>
                <td>${formatTimestamp(analysis.timestamp)}</td>
                <td>
                    <span class="badge bg-primary">${analysis.winning_seat}</span>
                </td>
                <td>${analysis.brightness_analysis}</td>
                <td>
                    <div class="small">
                        <strong>Type:</strong> ${getHandTypeBadge(leftPos.hand_type || 'Unknown')}<br>
                        <strong>Cards:</strong> ${leftPos.card_count || 0}
                    </div>
                </td>
                <td>
                    <div class="small">
                        <strong>Type:</strong> ${getHandTypeBadge(centerPos.hand_type || 'Unknown')}<br>
                        <strong>Cards:</strong> ${centerPos.card_count || 0}
                    </div>
                </td>
                <td>
                    <div class="small">
                        <strong>Type:</strong> ${getHandTypeBadge(rightPos.hand_type || 'Unknown')}<br>
                        <strong>Cards:</strong> ${rightPos.card_count || 0}
                    </div>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="viewAnalysisDetails(${analysis.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="copyAnalysisData(${analysis.id})">
                        <i class="fas fa-copy"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');
    
    document.getElementById('visibleRecords').textContent = analyses.length;
}

// Filter table based on search and seat filter
function filterTable() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const seatFilter = document.getElementById('seatFilter').value;
    
    let filteredAnalyses = allAnalyses.filter(analysis => {
        const matchesSearch = !searchTerm || 
            analysis.id.toString().includes(searchTerm) ||
            analysis.winning_seat.toLowerCase().includes(searchTerm) ||
            analysis.brightness_analysis.toLowerCase().includes(searchTerm) ||
            analysis.positions_summary.toLowerCase().includes(searchTerm);
        
        const matchesSeat = !seatFilter || analysis.winning_seat === seatFilter;
        
        return matchesSearch && matchesSeat;
    });
    
    displayAnalyses(filteredAnalyses);
}

// Sort table by column
function sortTable(columnIndex) {
    if (currentSort.column === columnIndex) {
        currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
    } else {
        currentSort.column = columnIndex;
        currentSort.direction = 'asc';
    }
    
    allAnalyses.sort((a, b) => {
        let aVal, bVal;
        
        switch (columnIndex) {
            case 0: // ID
                aVal = a.id;
                bVal = b.id;
                break;
            case 1: // Timestamp
                aVal = new Date(a.timestamp);
                bVal = new Date(b.timestamp);
                break;
            case 2: // Winning Seat
                aVal = a.winning_seat;
                bVal = b.winning_seat;
                break;
            case 3: // Brightness
                aVal = a.brightness_analysis;
                bVal = b.brightness_analysis;
                break;
            default:
                return 0;
        }
        
        if (aVal < bVal) return currentSort.direction === 'asc' ? -1 : 1;
        if (aVal > bVal) return currentSort.direction === 'asc' ? 1 : -1;
        return 0;
    });
    
    filterTable(); // Re-apply filters after sorting
}

// Update statistics
function updateStats() {
    document.getElementById('totalRecords').textContent = allAnalyses.length;
    document.getElementById('lastUpdated').textContent = new Date().toLocaleTimeString();
}

// View analysis details
async function viewAnalysisDetails(sessionId) {
    const analysis = allAnalyses.find(a => a.id === sessionId);
    if (!analysis || !analysis.details) return;
    
    const details = analysis.details;
    const modalBody = document.getElementById('analysis-details');
    
    modalBody.innerHTML = `
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h6><i class="fas fa-info-circle"></i> Session Information</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>Session ID:</strong></td><td>${details.id}</td></tr>
                            <tr><td><strong>Timestamp:</strong></td><td>${formatTimestamp(details.timestamp)}</td></tr>
                            <tr><td><strong>Winning Seat:</strong></td><td><span class="badge bg-primary">${details.winning_seat}</span></td></tr>
                            <tr><td><strong>Brightness Analysis:</strong></td><td>${details.brightness_analysis}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h6><i class="fas fa-crosshairs"></i> Region Coordinates</h6>
                    </div>
                    <div class="card-body">
                        <pre class="bg-light p-2 rounded small">${JSON.stringify(details.region_coords, null, 2)}</pre>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <h6><i class="fas fa-cards"></i> Card Positions Analysis</h6>
            </div>
        </div>
        
        <div class="row">
            ${details.positions?.map(pos => `
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-header ${pos.position === 'LEFT' ? 'bg-success' : pos.position === 'CENTER' ? 'bg-warning' : 'bg-danger'} text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-chair"></i> ${pos.position} - Seat ${pos.seat_name}
                            </h6>
                        </div>
                        <div class="card-body">
                            <p><strong>Hand Type:</strong><br>${getHandTypeBadge(pos.hand_type)}</p>
                            <p><strong>Cards Detected (${pos.card_count}):</strong></p>
                            <ul class="list-unstyled">
                                ${pos.cards.map(card => `<li><i class="fas fa-playing-card"></i> ${card}</li>`).join('') || '<li class="text-muted">No cards detected</li>'}
                            </ul>
                        </div>
                    </div>
                </div>
            `).join('') || '<div class="col-12"><p class="text-muted">No position data available</p></div>'}
        </div>
        
        ${details.raw_response ? `
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h6><i class="fas fa-robot"></i> Raw Gemini AI Response</h6>
                        </div>
                        <div class="card-body">
                            <pre class="bg-light p-3 rounded small" style="max-height: 300px; overflow-y: auto;">${details.raw_response}</pre>
                        </div>
                    </div>
                </div>
            </div>
        ` : ''}
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('analysisModal'));
    modal.show();
}

// Copy analysis data to clipboard
async function copyAnalysisData(sessionId) {
    const analysis = allAnalyses.find(a => a.id === sessionId);
    if (!analysis) return;
    
    try {
        await navigator.clipboard.writeText(JSON.stringify(analysis, null, 2));
        
        // Show success feedback
        const button = event.target.closest('button');
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.add('btn-success');
        
        setTimeout(() => {
            button.innerHTML = originalHTML;
            button.classList.remove('btn-success');
        }, 2000);
        
    } catch (error) {
        console.error('Failed to copy data:', error);
        alert('Failed to copy data to clipboard');
    }
}

// Export data
async function exportData() {
    try {
        const response = await fetch('/api/export-data');
        const data = await response.json();
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `card_analysis_export_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
        
    } catch (error) {
        console.error('Export failed:', error);
        alert('Failed to export data');
    }
}

// Print analysis
function printAnalysis() {
    window.print();
}

// Initialize page
document.addEventListener('DOMContentLoaded', loadAnalyses);

// Auto-refresh every 60 seconds
setInterval(loadAnalyses, 60000);
</script>
{% endblock %}
