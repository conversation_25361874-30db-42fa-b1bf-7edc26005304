{% extends "base.html" %}

{% block title %}Statistics - Card Analysis{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4"><i class="fas fa-chart-bar"></i> Statistics & Analytics</h1>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card stats-card bg-primary text-white">
            <div class="card-body text-center">
                <h3 id="total-sessions">-</h3>
                <p class="mb-0">Total Sessions</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card bg-success text-white">
            <div class="card-body text-center">
                <h3 id="seat-a-total">-</h3>
                <p class="mb-0">Seat A Wins</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card bg-warning text-white">
            <div class="card-body text-center">
                <h3 id="seat-b-total">-</h3>
                <p class="mb-0">Seat B Wins</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card bg-danger text-white">
            <div class="card-body text-center">
                <h3 id="seat-c-total">-</h3>
                <p class="mb-0">Seat C Wins</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card bg-info text-white">
            <div class="card-body text-center">
                <h3 id="win-rate-a">-%</h3>
                <p class="mb-0">A Win Rate</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card stats-card bg-secondary text-white">
            <div class="card-body text-center">
                <h3 id="avg-per-day">-</h3>
                <p class="mb-0">Avg/Day</p>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 1 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> Winning Seat Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="winningSeatChart" height="300"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-doughnut"></i> Hand Type Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="handTypeChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 2 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line"></i> Activity Timeline (Last 30 Days)</h5>
            </div>
            <div class="card-body">
                <canvas id="timelineChart" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar"></i> Hourly Activity</h5>
            </div>
            <div class="card-body">
                <canvas id="hourlyChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Statistics Tables -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-trophy"></i> Winning Seat Statistics</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Seat</th>
                                <th>Wins</th>
                                <th>Win Rate</th>
                                <th>Trend</th>
                            </tr>
                        </thead>
                        <tbody id="seat-stats-table">
                            <tr><td colspan="4" class="text-center">Loading...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-cards"></i> Hand Type Statistics</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Hand Type</th>
                                <th>Count</th>
                                <th>Percentage</th>
                                <th>Most Common Seat</th>
                            </tr>
                        </thead>
                        <tbody id="hand-type-stats-table">
                            <tr><td colspan="4" class="text-center">Loading...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity Table -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-calendar-alt"></i> Daily Activity Summary</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Date</th>
                                <th>Total Analyses</th>
                                <th>Seat A Wins</th>
                                <th>Seat B Wins</th>
                                <th>Seat C Wins</th>
                                <th>Most Active Hour</th>
                            </tr>
                        </thead>
                        <tbody id="daily-activity-table">
                            <tr><td colspan="6" class="text-center">Loading...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let statisticsData = {};
let allAnalyses = [];

// Load statistics data
async function loadStatistics() {
    try {
        // Load basic statistics
        const statsResponse = await fetch('/api/statistics');
        statisticsData = await statsResponse.json();
        
        // Load detailed analyses for advanced statistics
        const analysesResponse = await fetch('/api/recent-analyses?limit=1000');
        allAnalyses = await analysesResponse.json();
        
        // Load detailed data for each analysis
        for (let analysis of allAnalyses.slice(0, 100)) { // Limit to 100 for performance
            try {
                const detailResponse = await fetch(`/api/analysis/${analysis.id}`);
                const details = await detailResponse.json();
                analysis.details = details;
            } catch (e) {
                console.warn(`Failed to load details for analysis ${analysis.id}`);
            }
        }
        
        updateSummaryCards();
        createCharts();
        updateTables();
        
    } catch (error) {
        console.error('Error loading statistics:', error);
    }
}

// Update summary cards
function updateSummaryCards() {
    const stats = statisticsData;
    const total = stats.total_sessions || 0;
    const aWins = stats.winning_distribution?.A || 0;
    const bWins = stats.winning_distribution?.B || 0;
    const cWins = stats.winning_distribution?.C || 0;
    
    document.getElementById('total-sessions').textContent = total;
    document.getElementById('seat-a-total').textContent = aWins;
    document.getElementById('seat-b-total').textContent = bWins;
    document.getElementById('seat-c-total').textContent = cWins;
    
    // Calculate win rates
    const aRate = total > 0 ? Math.round((aWins / total) * 100) : 0;
    document.getElementById('win-rate-a').textContent = aRate + '%';
    
    // Calculate average per day
    const recentDays = stats.recent_activity?.length || 1;
    const avgPerDay = recentDays > 0 ? Math.round(total / recentDays) : 0;
    document.getElementById('avg-per-day').textContent = avgPerDay;
}

// Create all charts
function createCharts() {
    createWinningSeatChart();
    createHandTypeChart();
    createTimelineChart();
    createHourlyChart();
}

// Create winning seat distribution chart
function createWinningSeatChart() {
    const ctx = document.getElementById('winningSeatChart').getContext('2d');
    const distribution = statisticsData.winning_distribution || {};
    
    new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['Seat A (Left)', 'Seat B (Center)', 'Seat C (Right)'],
            datasets: [{
                data: [distribution.A || 0, distribution.B || 0, distribution.C || 0],
                backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                borderWidth: 3,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        font: { size: 14 }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? Math.round((context.raw / total) * 100) : 0;
                            return `${context.label}: ${context.raw} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// Create hand type distribution chart
function createHandTypeChart() {
    const ctx = document.getElementById('handTypeChart').getContext('2d');
    
    // Calculate hand type distribution from detailed analyses
    const handTypes = {};
    allAnalyses.forEach(analysis => {
        if (analysis.details && analysis.details.positions) {
            analysis.details.positions.forEach(pos => {
                const handType = pos.hand_type || 'Unknown';
                handTypes[handType] = (handTypes[handType] || 0) + 1;
            });
        }
    });
    
    const labels = Object.keys(handTypes);
    const data = Object.values(handTypes);
    const colors = ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1', '#fd7e14'];
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.slice(0, labels.length),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        font: { size: 12 }
                    }
                }
            }
        }
    });
}

// Create timeline chart
function createTimelineChart() {
    const ctx = document.getElementById('timelineChart').getContext('2d');
    const activityData = statisticsData.recent_activity || [];
    
    // Generate last 30 days data
    const last30Days = [];
    for (let i = 29; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];
        
        const dayData = activityData.find(item => item[0] === dateStr);
        last30Days.push({
            date: dateStr,
            count: dayData ? dayData[1] : 0
        });
    }
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: last30Days.map(item => new Date(item.date).toLocaleDateString()),
            datasets: [{
                label: 'Analyses per Day',
                data: last30Days.map(item => item.count),
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4,
                fill: true,
                pointBackgroundColor: '#007bff',
                pointBorderColor: '#fff',
                pointBorderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: { stepSize: 1 }
                },
                x: {
                    ticks: {
                        maxTicksLimit: 10
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

// Create hourly activity chart
function createHourlyChart() {
    const ctx = document.getElementById('hourlyChart').getContext('2d');
    
    // Calculate hourly distribution
    const hourlyData = new Array(24).fill(0);
    allAnalyses.forEach(analysis => {
        const hour = new Date(analysis.timestamp).getHours();
        hourlyData[hour]++;
    });
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: Array.from({length: 24}, (_, i) => `${i}:00`),
            datasets: [{
                label: 'Analyses',
                data: hourlyData,
                backgroundColor: 'rgba(0, 123, 255, 0.7)',
                borderColor: '#007bff',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: { stepSize: 1 }
                },
                x: {
                    ticks: {
                        maxTicksLimit: 12
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

// Update statistics tables
function updateTables() {
    updateSeatStatsTable();
    updateHandTypeStatsTable();
    updateDailyActivityTable();
}

// Update seat statistics table
function updateSeatStatsTable() {
    const distribution = statisticsData.winning_distribution || {};
    const total = statisticsData.total_sessions || 0;
    
    const seats = [
        { name: 'A (Left)', wins: distribution.A || 0, color: 'success' },
        { name: 'B (Center)', wins: distribution.B || 0, color: 'warning' },
        { name: 'C (Right)', wins: distribution.C || 0, color: 'danger' }
    ];
    
    const tableBody = document.getElementById('seat-stats-table');
    tableBody.innerHTML = seats.map(seat => {
        const winRate = total > 0 ? Math.round((seat.wins / total) * 100) : 0;
        const trend = seat.wins > (total / 3) ? '📈' : seat.wins < (total / 3) ? '📉' : '➡️';
        
        return `
            <tr>
                <td><span class="badge bg-${seat.color}">Seat ${seat.name}</span></td>
                <td>${seat.wins}</td>
                <td>${winRate}%</td>
                <td>${trend}</td>
            </tr>
        `;
    }).join('');
}

// Update hand type statistics table
function updateHandTypeStatsTable() {
    const handTypes = {};
    let totalHands = 0;
    
    allAnalyses.forEach(analysis => {
        if (analysis.details && analysis.details.positions) {
            analysis.details.positions.forEach(pos => {
                const handType = pos.hand_type || 'Unknown';
                if (!handTypes[handType]) {
                    handTypes[handType] = { count: 0, seats: { A: 0, B: 0, C: 0 } };
                }
                handTypes[handType].count++;
                handTypes[handType].seats[pos.seat_name] = (handTypes[handType].seats[pos.seat_name] || 0) + 1;
                totalHands++;
            });
        }
    });
    
    const tableBody = document.getElementById('hand-type-stats-table');
    tableBody.innerHTML = Object.entries(handTypes).map(([handType, data]) => {
        const percentage = totalHands > 0 ? Math.round((data.count / totalHands) * 100) : 0;
        const mostCommonSeat = Object.entries(data.seats).reduce((a, b) => a[1] > b[1] ? a : b)[0];
        
        return `
            <tr>
                <td>${getHandTypeBadge(handType)}</td>
                <td>${data.count}</td>
                <td>${percentage}%</td>
                <td><span class="badge bg-primary">Seat ${mostCommonSeat}</span></td>
            </tr>
        `;
    }).join('');
}

// Update daily activity table
function updateDailyActivityTable() {
    const activityData = statisticsData.recent_activity || [];
    
    const tableBody = document.getElementById('daily-activity-table');
    
    if (activityData.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="6" class="text-center">No activity data available</td></tr>';
        return;
    }
    
    tableBody.innerHTML = activityData.slice(0, 10).map(([date, count]) => {
        // Calculate seat distribution for this date
        const dayAnalyses = allAnalyses.filter(a => a.timestamp.startsWith(date));
        const seatCounts = { A: 0, B: 0, C: 0 };
        dayAnalyses.forEach(a => {
            if (seatCounts[a.winning_seat] !== undefined) {
                seatCounts[a.winning_seat]++;
            }
        });
        
        // Find most active hour (simplified)
        const hours = dayAnalyses.map(a => new Date(a.timestamp).getHours());
        const mostActiveHour = hours.length > 0 ? Math.round(hours.reduce((a, b) => a + b) / hours.length) : '-';
        
        return `
            <tr>
                <td>${new Date(date).toLocaleDateString()}</td>
                <td><span class="badge bg-primary">${count}</span></td>
                <td><span class="badge bg-success">${seatCounts.A}</span></td>
                <td><span class="badge bg-warning">${seatCounts.B}</span></td>
                <td><span class="badge bg-danger">${seatCounts.C}</span></td>
                <td>${mostActiveHour !== '-' ? mostActiveHour + ':00' : '-'}</td>
            </tr>
        `;
    }).join('');
}

// Initialize page
document.addEventListener('DOMContentLoaded', loadStatistics);

// Auto-refresh every 2 minutes
setInterval(loadStatistics, 120000);
</script>
{% endblock %}
