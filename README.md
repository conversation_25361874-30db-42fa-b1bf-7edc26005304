# Card Detection & Analysis Application

An intelligent card game analysis application using Google's Gemini AI API for real-time card detection and analysis with database storage and web dashboard.

## Features

### 🎯 Core Analysis Features
- **Screen Region Selection**: Click and drag to select the game area
- **Real-time Analysis**: Captures and analyzes cards after a 14.5-second countdown
- **Card Recognition**: Identifies suits, numbers, and card combinations
- **Hand Analysis**: Determines hand types (High Card, Pair, Sequence, Color, Straight Flush)
- **Winning Seat Detection**: Analyzes brightness to determine winning position
- **Three-Position Analysis**: Separate analysis for Left (A), Center (B), and Right (C) positions

### 📊 Database & Web Features
- **SQLite Database**: Automatic storage of all analysis results
- **Web Dashboard**: Beautiful web interface to view stored data
- **Data Tables**: Sortable, filterable tables with all analysis history
- **Statistics & Charts**: Visual analytics with charts and graphs
- **Export Functionality**: Export data as JSON for external analysis
- **Real-time Updates**: Auto-refreshing dashboard with latest results

## Installation

1. **Install Python Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure API Key**:
   - Your Gemini API key is already configured in `config.py`
   - If you need to change it, edit the `GEMINI_API_KEY` variable

## Usage

### 🚀 Quick Start (Complete System)
```bash
python start_complete_system.py
```
This starts both the GUI analyzer and web dashboard automatically.

### 📱 Individual Components

1. **Card Analyzer GUI Only**:
   ```bash
   python card_analyzer.py
   ```

2. **Web Dashboard Only**:
   ```bash
   python web_app.py
   ```
   Then visit: http://localhost:5000

### 🎮 How to Use

1. **Select Screen Region**:
   - Click "Select Screen Region" in the GUI
   - Click and drag to select the card game area
   - Release to confirm selection

2. **Analyze Cards**:
   - Wait for the countdown to reach 1
   - Press "ANALYZE NOW!" button
   - Wait 14.5 seconds for automatic capture and analysis

3. **View Results**:
   - Results appear in the GUI immediately
   - Click "Open Web Dashboard" to view in browser
   - All data is automatically saved to database

## How It Works

1. **Region Selection**: User selects the screen area containing the card game
2. **Countdown Timer**: 14.5-second delay allows for game timing
3. **Screen Capture**: Captures the selected region
4. **AI Analysis**: Gemini AI analyzes the image for:
   - Card identification (suits and values)
   - Hand type classification
   - Brightness analysis for winning seat
5. **Results Display**: Shows detailed analysis for all three positions

## Output Format

The application provides:

- **Left Side (Seat A)**: Cards, suits, numbers, hand type
- **Center (Seat B)**: Cards, suits, numbers, hand type  
- **Right Side (Seat C)**: Cards, suits, numbers, hand type
- **Winning Seat**: A (left), B (center), or C (right) based on brightness

## Hand Types Detected

- **High Card**: No special combination
- **Pair**: Two cards of the same value
- **Sequence**: Three consecutive cards
- **Color**: Three cards of the same suit
- **Straight Flush**: Sequence of the same suit

## 🌐 Web Dashboard Features

### Dashboard Pages
- **Main Dashboard**: Overview with statistics and recent analyses
- **All Analyses**: Complete table of all stored data with filtering
- **Statistics**: Charts and detailed analytics

### Data Management
- **Automatic Storage**: All analyses saved to SQLite database
- **Export Data**: Download complete dataset as JSON
- **Search & Filter**: Find specific analyses quickly
- **Sort Columns**: Click column headers to sort data

## 📁 Project Structure

```
Card-Detection/
├── card_analyzer.py          # Main GUI application
├── web_app.py               # Flask web dashboard
├── database.py              # Database operations
├── config.py                # Configuration settings
├── utils.py                 # Utility functions
├── requirements.txt         # Python dependencies
├── start_complete_system.py # Complete system launcher
├── run_analyzer.py          # GUI-only launcher
├── templates/               # Web templates
│   ├── base.html           # Base template
│   ├── dashboard.html      # Main dashboard
│   ├── analyses.html       # Data tables
│   └── statistics.html     # Analytics page
└── card_analysis.db        # SQLite database (created automatically)
```

## ⚙️ Configuration

Edit `config.py` to modify:
- API key
- Countdown delay time
- Hand type definitions
- Winning seat mappings

## 🔧 Troubleshooting

1. **Import Errors**: Run `pip install -r requirements.txt`
2. **API Errors**: Check your Gemini API key in `config.py`
3. **Screen Capture Issues**: Ensure proper permissions for screen capture
4. **Analysis Errors**: Check internet connection for Gemini API access
5. **Web Dashboard Issues**: Ensure port 5000 is available
6. **Database Errors**: Check write permissions in project directory

## 📋 Requirements

- Python 3.7+
- Google Gemini API key
- Screen capture permissions
- Internet connection for AI analysis
- Web browser for dashboard access
