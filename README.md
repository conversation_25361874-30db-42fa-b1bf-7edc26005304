# Card Detection & Analysis Application

An intelligent card game analysis application using Google's Gemini AI API for real-time card detection and analysis.

## Features

- **Screen Region Selection**: Click and drag to select the game area
- **Real-time Analysis**: Captures and analyzes cards after a 14.5-second countdown
- **Card Recognition**: Identifies suits, numbers, and card combinations
- **Hand Analysis**: Determines hand types (High Card, Pair, Sequence, Color, Straight Flush)
- **Winning Seat Detection**: Analyzes brightness to determine winning position
- **Three-Position Analysis**: Separate analysis for Left (A), Center (B), and Right (C) positions

## Installation

1. **Install Python Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure API Key**:
   - Your Gemini API key is already configured in `config.py`
   - If you need to change it, edit the `GEMINI_API_KEY` variable

## Usage

1. **Run the Application**:
   ```bash
   python run_analyzer.py
   ```
   or
   ```bash
   python card_analyzer.py
   ```

2. **Select Screen Region**:
   - Click "Select Screen Region"
   - Click and drag to select the card game area
   - Release to confirm selection

3. **Analyze Cards**:
   - Wait for the countdown to reach 1
   - Press "ANALYZE NOW!" button
   - Wait 14.5 seconds for automatic capture and analysis

## How It Works

1. **Region Selection**: User selects the screen area containing the card game
2. **Countdown Timer**: 14.5-second delay allows for game timing
3. **Screen Capture**: Captures the selected region
4. **AI Analysis**: Gemini AI analyzes the image for:
   - Card identification (suits and values)
   - Hand type classification
   - Brightness analysis for winning seat
5. **Results Display**: Shows detailed analysis for all three positions

## Output Format

The application provides:

- **Left Side (Seat A)**: Cards, suits, numbers, hand type
- **Center (Seat B)**: Cards, suits, numbers, hand type  
- **Right Side (Seat C)**: Cards, suits, numbers, hand type
- **Winning Seat**: A (left), B (center), or C (right) based on brightness

## Hand Types Detected

- **High Card**: No special combination
- **Pair**: Two cards of the same value
- **Sequence**: Three consecutive cards
- **Color**: Three cards of the same suit
- **Straight Flush**: Sequence of the same suit

## Configuration

Edit `config.py` to modify:
- API key
- Countdown delay time
- Hand type definitions
- Winning seat mappings

## Troubleshooting

1. **Import Errors**: Run `pip install -r requirements.txt`
2. **API Errors**: Check your Gemini API key in `config.py`
3. **Screen Capture Issues**: Ensure proper permissions for screen capture
4. **Analysis Errors**: Check internet connection for Gemini API access

## Requirements

- Python 3.7+
- Google Gemini API key
- Screen capture permissions
- Internet connection for AI analysis
