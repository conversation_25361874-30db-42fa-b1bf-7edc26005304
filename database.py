"""
Database module for Card Detection & Analysis Application
Handles storage and retrieval of analysis results
"""

import sqlite3
import json
from datetime import datetime
from typing import Dict, List, Optional
import os

class CardAnalysisDB:
    def __init__(self, db_path: str = "card_analysis.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the database with required tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create analysis_sessions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS analysis_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                winning_seat TEXT,
                brightness_analysis TEXT,
                raw_gemini_response TEXT,
                region_coords TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create card_positions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS card_positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id INTEGER,
                position TEXT,  -- 'LEFT', 'CENTER', 'RIGHT'
                seat_name TEXT, -- 'A', 'B', 'C'
                hand_type TEXT,
                cards_detected TEXT, -- JSON array of cards
                card_count INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES analysis_sessions (id)
            )
        ''')
        
        # Create individual_cards table for detailed card tracking
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS individual_cards (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                position_id INTEGER,
                card_suit TEXT,
                card_value TEXT,
                card_full_name TEXT,
                confidence_score REAL DEFAULT 0.0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (position_id) REFERENCES card_positions (id)
            )
        ''')
        
        # Create analysis_statistics table for summary data
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS analysis_statistics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date DATE DEFAULT (DATE('now')),
                total_analyses INTEGER DEFAULT 0,
                winning_seat_a_count INTEGER DEFAULT 0,
                winning_seat_b_count INTEGER DEFAULT 0,
                winning_seat_c_count INTEGER DEFAULT 0,
                most_common_hand_type TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def save_analysis_result(self, analysis_data: Dict) -> int:
        """Save complete analysis result to database"""
        conn = sqlite3.connect(self.db_path, timeout=30.0)
        conn.execute('PRAGMA journal_mode=WAL')  # Enable WAL mode for better concurrency
        cursor = conn.cursor()
        
        try:
            # Insert main session record
            cursor.execute('''
                INSERT INTO analysis_sessions 
                (winning_seat, brightness_analysis, raw_gemini_response, region_coords)
                VALUES (?, ?, ?, ?)
            ''', (
                analysis_data.get('winning_seat', 'Unknown'),
                analysis_data.get('brightness_analysis', ''),
                analysis_data.get('raw_response', ''),
                json.dumps(analysis_data.get('region_coords', {}))
            ))
            
            session_id = cursor.lastrowid
            
            # Insert position data
            positions = ['left_cards', 'center_cards', 'right_cards']
            seat_names = ['A', 'B', 'C']
            position_names = ['LEFT', 'CENTER', 'RIGHT']
            
            for i, pos_key in enumerate(positions):
                if pos_key in analysis_data:
                    pos_data = analysis_data[pos_key]
                    cards_json = json.dumps(pos_data.get('cards', []))
                    
                    cursor.execute('''
                        INSERT INTO card_positions 
                        (session_id, position, seat_name, hand_type, cards_detected, card_count)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (
                        session_id,
                        position_names[i],
                        seat_names[i],
                        pos_data.get('hand_type', 'Unknown'),
                        cards_json,
                        len(pos_data.get('cards', []))
                    ))
                    
                    position_id = cursor.lastrowid
                    
                    # Insert individual cards if available
                    for card in pos_data.get('cards', []):
                        if isinstance(card, str) and card.strip():
                            # Try to parse card string (e.g., "Ace of Hearts", "10 of Spades")
                            suit, value = self.parse_card_string(card)
                            cursor.execute('''
                                INSERT INTO individual_cards 
                                (position_id, card_suit, card_value, card_full_name)
                                VALUES (?, ?, ?, ?)
                            ''', (position_id, suit, value, card))
            
            # Update statistics
            self.update_daily_statistics(analysis_data.get('winning_seat', 'Unknown'))
            
            conn.commit()
            return session_id
            
        except Exception as e:
            conn.rollback()
            print(f"Error saving analysis result: {e}")
            return -1
        finally:
            conn.close()
    
    def parse_card_string(self, card_str: str) -> tuple:
        """Parse card string to extract suit and value"""
        try:
            # Common patterns: "Ace of Hearts", "10 of Spades", "King Hearts", etc.
            card_str = card_str.strip()
            
            # Extract suit
            suits = ['Hearts', 'Diamonds', 'Clubs', 'Spades', '♠', '♥', '♦', '♣']
            suit = 'Unknown'
            for s in suits:
                if s in card_str:
                    suit = s
                    break
            
            # Extract value
            values = ['A', 'Ace', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Jack', 'Q', 'Queen', 'K', 'King']
            value = 'Unknown'
            for v in values:
                if v in card_str:
                    value = v
                    break
            
            return suit, value
        except:
            return 'Unknown', 'Unknown'
    
    def update_daily_statistics(self, winning_seat: str):
        """Update daily statistics"""
        conn = sqlite3.connect(self.db_path, timeout=30.0)
        conn.execute('PRAGMA journal_mode=WAL')
        cursor = conn.cursor()
        
        try:
            today = datetime.now().date()
            
            # Check if record exists for today
            cursor.execute('SELECT id FROM analysis_statistics WHERE date = ?', (today,))
            existing = cursor.fetchone()
            
            if existing:
                # Update existing record
                update_query = '''
                    UPDATE analysis_statistics 
                    SET total_analyses = total_analyses + 1,
                        winning_seat_a_count = winning_seat_a_count + ?,
                        winning_seat_b_count = winning_seat_b_count + ?,
                        winning_seat_c_count = winning_seat_c_count + ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE date = ?
                '''
                cursor.execute(update_query, (
                    1 if winning_seat == 'A' else 0,
                    1 if winning_seat == 'B' else 0,
                    1 if winning_seat == 'C' else 0,
                    today
                ))
            else:
                # Create new record
                cursor.execute('''
                    INSERT INTO analysis_statistics 
                    (date, total_analyses, winning_seat_a_count, winning_seat_b_count, winning_seat_c_count)
                    VALUES (?, 1, ?, ?, ?)
                ''', (
                    today,
                    1 if winning_seat == 'A' else 0,
                    1 if winning_seat == 'B' else 0,
                    1 if winning_seat == 'C' else 0
                ))
            
            conn.commit()
        except Exception as e:
            print(f"Error updating statistics: {e}")
            conn.rollback()
        finally:
            conn.close()
    
    def get_recent_analyses(self, limit: int = 50) -> List[Dict]:
        """Get recent analysis results"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                SELECT 
                    s.id, s.timestamp, s.winning_seat, s.brightness_analysis,
                    GROUP_CONCAT(p.position || ':' || p.hand_type, '; ') as positions_summary
                FROM analysis_sessions s
                LEFT JOIN card_positions p ON s.id = p.session_id
                GROUP BY s.id, s.timestamp, s.winning_seat, s.brightness_analysis
                ORDER BY s.timestamp DESC
                LIMIT ?
            ''', (limit,))
            
            results = []
            for row in cursor.fetchall():
                results.append({
                    'id': row[0],
                    'timestamp': row[1],
                    'winning_seat': row[2],
                    'brightness_analysis': row[3],
                    'positions_summary': row[4] or ''
                })
            
            return results
        except Exception as e:
            print(f"Error fetching recent analyses: {e}")
            return []
        finally:
            conn.close()
    
    def get_analysis_details(self, session_id: int) -> Dict:
        """Get detailed analysis for a specific session"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Get session info
            cursor.execute('''
                SELECT timestamp, winning_seat, brightness_analysis, raw_gemini_response, region_coords
                FROM analysis_sessions WHERE id = ?
            ''', (session_id,))
            
            session_data = cursor.fetchone()
            if not session_data:
                return {}
            
            # Get position details
            cursor.execute('''
                SELECT position, seat_name, hand_type, cards_detected, card_count
                FROM card_positions WHERE session_id = ?
                ORDER BY position
            ''', (session_id,))
            
            positions = []
            for row in cursor.fetchall():
                cards = json.loads(row[3]) if row[3] else []
                positions.append({
                    'position': row[0],
                    'seat_name': row[1],
                    'hand_type': row[2],
                    'cards': cards,
                    'card_count': row[4]
                })
            
            return {
                'id': session_id,
                'timestamp': session_data[0],
                'winning_seat': session_data[1],
                'brightness_analysis': session_data[2],
                'raw_response': session_data[3],
                'region_coords': json.loads(session_data[4]) if session_data[4] else {},
                'positions': positions
            }
            
        except Exception as e:
            print(f"Error fetching analysis details: {e}")
            return {}
        finally:
            conn.close()
    
    def get_statistics_summary(self) -> Dict:
        """Get overall statistics summary"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Get total counts
            cursor.execute('SELECT COUNT(*) FROM analysis_sessions')
            total_sessions = cursor.fetchone()[0]
            
            # Get winning seat distribution
            cursor.execute('''
                SELECT winning_seat, COUNT(*) 
                FROM analysis_sessions 
                WHERE winning_seat != 'Unknown'
                GROUP BY winning_seat
            ''')
            winning_distribution = dict(cursor.fetchall())
            
            # Get recent activity (last 7 days)
            cursor.execute('''
                SELECT DATE(timestamp) as date, COUNT(*) as count
                FROM analysis_sessions 
                WHERE timestamp >= datetime('now', '-7 days')
                GROUP BY DATE(timestamp)
                ORDER BY date DESC
            ''')
            recent_activity = cursor.fetchall()
            
            return {
                'total_sessions': total_sessions,
                'winning_distribution': winning_distribution,
                'recent_activity': recent_activity
            }
            
        except Exception as e:
            print(f"Error fetching statistics: {e}")
            return {}
        finally:
            conn.close()
