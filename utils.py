import cv2
import numpy as np
from PIL import Image
import base64
import io
from typing import Dict, List, Tuple, Optional

def image_to_base64(image: Image.Image) -> str:
    """Convert PIL Image to base64 string for Gemini API"""
    buffer = io.BytesIO()
    image.save(buffer, format='PNG')
    img_str = base64.b64encode(buffer.getvalue()).decode()
    return img_str

def analyze_brightness_regions(image: Image.Image) -> str:
    """Analyze which region (left, center, right) is brightest"""
    # Convert PIL to OpenCV format
    cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
    height, width = cv_image.shape[:2]
    
    # Divide image into three regions
    left_region = cv_image[:, :width//3]
    center_region = cv_image[:, width//3:2*width//3]
    right_region = cv_image[:, 2*width//3:]
    
    # Calculate average brightness for each region
    left_brightness = np.mean(cv2.cvtColor(left_region, cv2.COLOR_BGR2GRAY))
    center_brightness = np.mean(cv2.cvtColor(center_region, cv2.COLOR_BGR2GRAY))
    right_brightness = np.mean(cv2.cvtColor(right_region, cv2.COLOR_BGR2GRAY))
    
    # Determine brightest region
    brightnesses = {
        'LEFT': left_brightness,
        'CENTER': center_brightness,
        'RIGHT': right_brightness
    }
    
    brightest_region = max(brightnesses, key=brightnesses.get)
    return brightest_region

def parse_card_analysis(gemini_response: str) -> Dict:
    """Parse Gemini API response into structured card analysis"""
    try:
        # This is a simplified parser - you might need to adjust based on actual Gemini responses
        analysis = {
            'left_cards': {'cards': [], 'hand_type': 'Unknown'},
            'center_cards': {'cards': [], 'hand_type': 'Unknown'},
            'right_cards': {'cards': [], 'hand_type': 'Unknown'},
            'winning_seat': 'Unknown'
        }
        
        # Split response into sections
        lines = gemini_response.split('\n')
        current_section = None
        
        for line in lines:
            line = line.strip()
            if 'left' in line.lower() or 'seat a' in line.lower():
                current_section = 'left_cards'
            elif 'center' in line.lower() or 'seat b' in line.lower():
                current_section = 'center_cards'
            elif 'right' in line.lower() or 'seat c' in line.lower():
                current_section = 'right_cards'
            elif 'winning' in line.lower():
                # Extract winning seat
                if 'A' in line or 'left' in line.lower():
                    analysis['winning_seat'] = 'A'
                elif 'B' in line or 'center' in line.lower():
                    analysis['winning_seat'] = 'B'
                elif 'C' in line or 'right' in line.lower():
                    analysis['winning_seat'] = 'C'
            
            # Extract card information and hand types
            if current_section and any(suit in line for suit in ['Hearts', 'Diamonds', 'Clubs', 'Spades', '♠', '♥', '♦', '♣']):
                if 'cards' not in analysis[current_section]:
                    analysis[current_section]['cards'] = []
                analysis[current_section]['cards'].append(line)
            
            # Extract hand types
            if current_section and any(hand_type in line.lower() for hand_type in ['pair', 'sequence', 'color', 'high card', 'straight']):
                analysis[current_section]['hand_type'] = line
        
        return analysis
    except Exception as e:
        print(f"Error parsing Gemini response: {e}")
        return {
            'left_cards': {'cards': [], 'hand_type': 'Parse Error'},
            'center_cards': {'cards': [], 'hand_type': 'Parse Error'},
            'right_cards': {'cards': [], 'hand_type': 'Parse Error'},
            'winning_seat': 'Unknown'
        }

def create_analysis_prompt() -> str:
    """Create detailed prompt for Gemini API"""
    return """
    Analyze this card game image and provide detailed information about:

    1. LEFT SIDE (3 cards): 
       - Identify each card (suit and number/face)
       - Determine hand type: High Card, Pair, Sequence, Color, or Straight Flush
    
    2. CENTER (3 cards):
       - Identify each card (suit and number/face)  
       - Determine hand type: High Card, Pair, Sequence, Color, or Straight Flush
    
    3. RIGHT SIDE (3 cards):
       - Identify each card (suit and number/face)
       - Determine hand type: High Card, Pair, Sequence, Color, or Straight Flush
    
    4. WINNING SEAT:
       - Determine which region (left, center, right) appears brightest
       - If LEFT is brightest: Winning seat is A
       - If CENTER is brightest: Winning seat is B  
       - If RIGHT is brightest: Winning seat is C
    
    Please format your response clearly with sections for each area.
    Use standard card notation (A, 2-10, J, Q, K for values and Hearts, Diamonds, Clubs, Spades for suits).
    """
