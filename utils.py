import cv2
import numpy as np
from PIL import Image
import base64
import io
from typing import Dict, List, Tuple, Optional

def image_to_base64(image: Image.Image) -> str:
    """Convert PIL Image to base64 string for Gemini API"""
    buffer = io.BytesIO()
    image.save(buffer, format='PNG')
    img_str = base64.b64encode(buffer.getvalue()).decode()
    return img_str

def analyze_brightness_regions(image: Image.Image) -> str:
    """Analyze which region (left, center, right) is brightest"""
    # Convert PIL to OpenCV format
    cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
    height, width = cv_image.shape[:2]
    
    # Divide image into three regions
    left_region = cv_image[:, :width//3]
    center_region = cv_image[:, width//3:2*width//3]
    right_region = cv_image[:, 2*width//3:]
    
    # Calculate average brightness for each region
    left_brightness = np.mean(cv2.cvtColor(left_region, cv2.COLOR_BGR2GRAY))
    center_brightness = np.mean(cv2.cvtColor(center_region, cv2.COLOR_BGR2GRAY))
    right_brightness = np.mean(cv2.cvtColor(right_region, cv2.COLOR_BGR2GRAY))
    
    # Determine brightest region
    brightnesses = {
        'LEFT': left_brightness,
        'CENTER': center_brightness,
        'RIGHT': right_brightness
    }
    
    brightest_region = max(brightnesses, key=brightnesses.get)
    return brightest_region

def parse_card_analysis(gemini_response: str) -> Dict:
    """Parse Gemini API response into structured card analysis"""
    try:
        print(f"Raw Gemini Response:\n{gemini_response}\n" + "="*50)

        analysis = {
            'left_cards': {'cards': [], 'hand_type': 'Unknown'},
            'center_cards': {'cards': [], 'hand_type': 'Unknown'},
            'right_cards': {'cards': [], 'hand_type': 'Unknown'},
            'winning_seat': 'Unknown'
        }

        # Split response into lines and process
        lines = gemini_response.split('\n')
        current_section = None

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Identify sections
            if 'left side' in line.lower() or 'seat a' in line.lower():
                current_section = 'left_cards'
                continue
            elif 'center' in line.lower() or 'seat b' in line.lower():
                current_section = 'center_cards'
                continue
            elif 'right side' in line.lower() or 'seat c' in line.lower():
                current_section = 'right_cards'
                continue
            elif 'winning seat' in line.lower():
                # Extract winning seat
                if 'A' in line:
                    analysis['winning_seat'] = 'A'
                elif 'B' in line:
                    analysis['winning_seat'] = 'B'
                elif 'C' in line:
                    analysis['winning_seat'] = 'C'
                continue

            # Extract cards
            if current_section and line.lower().startswith('cards:'):
                cards_text = line[6:].strip()  # Remove "Cards:" prefix
                if cards_text and cards_text.lower() != 'not detected':
                    # Split by comma and clean up
                    cards = [card.strip() for card in cards_text.split(',') if card.strip()]
                    analysis[current_section]['cards'] = cards
                continue

            # Extract hand types
            if current_section and line.lower().startswith('hand type:'):
                hand_type = line[10:].strip()  # Remove "Hand Type:" prefix
                analysis[current_section]['hand_type'] = hand_type
                continue

            # Alternative parsing for different formats
            if current_section:
                # Look for card patterns in any line
                if any(suit in line for suit in ['Hearts', 'Diamonds', 'Clubs', 'Spades', '♠', '♥', '♦', '♣']):
                    if 'cards:' not in line.lower():  # Avoid duplicating cards already parsed
                        analysis[current_section]['cards'].append(line)

                # Look for hand type patterns
                if any(hand_type in line.lower() for hand_type in ['pair', 'sequence', 'color', 'high card', 'straight', 'flush']):
                    if 'hand type:' not in line.lower():  # Avoid duplicating hand types already parsed
                        analysis[current_section]['hand_type'] = line

        # Debug output
        print(f"Parsed Analysis: {analysis}")

        return analysis

    except Exception as e:
        print(f"Error parsing Gemini response: {e}")
        return {
            'left_cards': {'cards': [], 'hand_type': 'Parse Error'},
            'center_cards': {'cards': [], 'hand_type': 'Parse Error'},
            'right_cards': {'cards': [], 'hand_type': 'Parse Error'},
            'winning_seat': 'Unknown'
        }

def create_analysis_prompt() -> str:
    """Create detailed prompt for Gemini API"""
    return """
    Analyze this card game image and provide detailed information. Look for playing cards in three distinct areas: LEFT, CENTER, and RIGHT.

    For each area, identify:
    1. LEFT SIDE (Seat A) - 3 cards:
       - List each card: [Value] of [Suit] (example: "Ace of Hearts", "10 of Spades")
       - Hand type: High Card, Pair, Sequence, Color, or Straight Flush

    2. CENTER (Seat B) - 3 cards:
       - List each card: [Value] of [Suit] (example: "King of Diamonds", "7 of Clubs")
       - Hand type: High Card, Pair, Sequence, Color, or Straight Flush

    3. RIGHT SIDE (Seat C) - 3 cards:
       - List each card: [Value] of [Suit] (example: "Queen of Hearts", "2 of Spades")
       - Hand type: High Card, Pair, Sequence, Color, or Straight Flush

    4. WINNING SEAT:
       - Determine which region (left, center, right) appears brightest or most illuminated
       - If LEFT is brightest: Winning seat is A
       - If CENTER is brightest: Winning seat is B
       - If RIGHT is brightest: Winning seat is C

    Format your response exactly like this:

    LEFT SIDE (Seat A):
    Cards: [card1], [card2], [card3]
    Hand Type: [type]

    CENTER (Seat B):
    Cards: [card1], [card2], [card3]
    Hand Type: [type]

    RIGHT SIDE (Seat C):
    Cards: [card1], [card2], [card3]
    Hand Type: [type]

    WINNING SEAT: [A/B/C]

    Use standard notation: A, 2, 3, 4, 5, 6, 7, 8, 9, 10, J, Q, K for values and Hearts, Diamonds, Clubs, Spades for suits.
    """
