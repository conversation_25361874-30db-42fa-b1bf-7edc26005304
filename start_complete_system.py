#!/usr/bin/env python3
"""
Complete Card Detection System Launcher
Starts both the card analyzer GUI and web dashboard
"""

import sys
import subprocess
import threading
import time
import webbrowser
from pathlib import Path

def check_dependencies():
    """Check if required packages are installed"""
    # Skip dependency check since packages are already installed
    return True

def start_web_server():
    """Start the Flask web server in a separate thread"""
    try:
        print("🌐 Starting web server...")
        from web_app import app
        app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)
    except Exception as e:
        print(f"❌ Error starting web server: {e}")

def start_card_analyzer():
    """Start the card analyzer GUI"""
    try:
        print("🎯 Starting card analyzer...")
        from card_analyzer import CardAnalyzer
        app = CardAnalyzer()
        app.run()
    except Exception as e:
        print(f"❌ Error starting card analyzer: {e}")

def open_dashboard():
    """Open web dashboard in browser after delay"""
    time.sleep(3)  # Wait for web server to start
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 Web dashboard opened in browser")
    except Exception as e:
        print(f"⚠️  Could not open browser automatically: {e}")
        print("📱 Please manually visit: http://localhost:5000")

def main():
    """Main launcher function"""
    print("🎮 Card Detection & Analysis System")
    print("=" * 50)
    print("🤖 Powered by Gemini AI")
    print("📊 With Database Storage & Web Dashboard")
    print("=" * 50)
    
    print("✅ Dependencies ready!")
    
    # Initialize database
    try:
        from database import CardAnalysisDB
        db = CardAnalysisDB()
        print("✅ Database initialized successfully!")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        input("\nPress Enter to exit...")
        sys.exit(1)
    
    print("\n🚀 Starting system components...")
    
    # Start web server in background thread
    web_thread = threading.Thread(target=start_web_server, daemon=True)
    web_thread.start()
    
    # Open dashboard in browser after delay
    browser_thread = threading.Thread(target=open_dashboard, daemon=True)
    browser_thread.start()
    
    # Wait a moment for web server to start
    time.sleep(2)
    
    print("\n" + "=" * 50)
    print("🎯 CARD ANALYZER GUI - Main Application")
    print("🌐 WEB DASHBOARD - http://localhost:5000")
    print("=" * 50)
    print("\n📋 Instructions:")
    print("1. Use the GUI to analyze cards")
    print("2. View results in the web dashboard")
    print("3. All data is automatically saved to database")
    print("\n🔄 Starting main application...")
    
    # Start main card analyzer (this will block until GUI is closed)
    start_card_analyzer()
    
    print("\n👋 Application closed. Thank you!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  System stopped by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        input("\nPress Enter to exit...")
    finally:
        print("🔚 Goodbye!")
