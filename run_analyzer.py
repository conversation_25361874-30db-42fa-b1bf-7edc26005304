#!/usr/bin/env python3
"""
Card Detection & Analysis Application Launcher
"""

import sys
import subprocess
import os

def check_dependencies():
    """Check if required packages are installed"""
    # Skip dependency check since packages are already installed
    return True

def main():
    """Main launcher function"""
    print("Card Detection & Analysis Application")
    print("=" * 40)
    
    # Dependencies ready
    
    # Import and run the application
    try:
        from card_analyzer import CardAnalyzer
        print("Starting application...")
        app = CardAnalyzer()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
